import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
%matplotlib inline
from matplotlib.backends.backend_pdf import PdfPages
import matplotlib.dates as mdates
import warnings
warnings.filterwarnings('ignore')
import time
import os
import sys
from scipy.stats.mstats import winsorize as wn
from dateutil.relativedelta import relativedelta
import math
pd.set_option('display.max_rows', 50)
from datetime import date, timedelta
from tqdm import tqdm
from datetime import datetime
import pyodbc
from matplotlib.pyplot import figure
from data import Data
d = Data()
import functools as ft
import yfinance as yf 
import matplotlib.gridspec as gridspec
import tempfile
from fpdf import FPDF
import seaborn as sns

start_time = time.perf_counter()

