import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
%matplotlib inline
from matplotlib.backends.backend_pdf import PdfPages
import matplotlib.dates as mdates
import warnings
warnings.filterwarnings('ignore')
import time
import os
import sys
from scipy.stats.mstats import winsorize as wn
from dateutil.relativedelta import relativedelta
import math
pd.set_option('display.max_rows', 50)
from datetime import date, timedelta
from tqdm import tqdm
from datetime import datetime
import pyodbc
from matplotlib.pyplot import figure
from data import Data
d = Data()
import functools as ft
import yfinance as yf 
import matplotlib.gridspec as gridspec
import tempfile
from fpdf import FPDF
import seaborn as sns
from matplotlib.lines import Line2D

start_time = time.perf_counter()

# Load the data
BetaRegime_Scores = pd.read_excel('BetaRegime_Scores.xlsx')
BetaRegime_Scores['Date'] = pd.to_datetime(BetaRegime_Scores['Date'], errors='coerce')

# Set up the figure and axis
fig, ax = plt.subplots(figsize=(12, 4))

# Plot the rolling mean of the agg_score
ax.plot(BetaRegime_Scores['Date'], BetaRegime_Scores['agg_score'].rolling(10).mean(), color='navy', label='Agg Score', linewidth=1.0)

# Plot horizontal lines for each regime
ax.axhline(y=0.75, color='green', linestyle='-', linewidth=2, label='HighBeta')
# ax.axhline(y=0, color='lightgoldenrodyellow', linestyle='-', linewidth=2, label='Neutral')
ax.axhline(y=0.25, color='red', linestyle='-', linewidth=2, label='LowBeta')

# Set labels and title
ax.set_xlabel('Date')
ax.set_ylabel('Agg Score')
ax.set_title('Smart Beta Regime')
ax.legend(title="Regimes")

# Show the plot
plt.tight_layout()
plt.show()


price_data = d.fetch_price_data(start_date='2006-01-01')

price_data['Symbol'] = price_data['Symbol'].astype(str).str.strip()
price_data = price_data[price_data['Date'] != '2022-03-07']

# Get the top 500 stocks for each date
top500 = price_data.groupby('Date').apply(lambda x: x.sort_values(by='Mcap', ascending=False).head(500)).reset_index(drop=True)

# Filter the original DataFrame based on top 500 stocks
price_data = price_data[price_data['Symbol'].isin(top500['Symbol'].unique())].reset_index(drop=True)

# Create a 'Rank' column based on market capitalization within each date
price_data['Rank'] = price_data.groupby('Date')['Mcap'].rank(ascending=False, method='first')

price_data['CapSize'] = np.where((price_data['Rank'] <=100), 
                                   'Large', 
                                   np.where((price_data['Rank'] >=251), 
                                            'Small', 
                                            'Mid'))
price_data.tail()

Value_rawdata = d.fetch_data_from_database(table_name='Value_rawdata', start_date='2006-01-01')
SectorThemeGics = d.fetch_data_from_database(table_name='SectorThemeGics')
master_nifty_data = pd.read_excel(r'\\*************\c$\Users\Administrator\PycharmProjects\Projects\SectorStyle_Data_Pipeline\latest_outputs\Style NAV2.xlsx')
master_nifty_data = master_nifty_data[['Date', 'NIFTY', 'NIFTYMIDCAP150','NIFTYSMALLCAP25', 'NIFTY500', 'NFT500EQUAL']]
master_nifty_data.rename(columns={
                                'NIFTY':'Large',
                                'NIFTYMIDCAP150':'Mid',
                                'NIFTYSMALLCAP25':'Small',
                                'NIFTY500':'Nifty 500',
                                'NFT500EQUAL':'Nifty 500 Equal Wtd',
                                }, 
                                inplace=True)

master_nifty_data['Date'] = pd.to_datetime(master_nifty_data['Date'])
valid_trading_dates = d.fetch_stock_data('NIFTY', start_date='2006-01-01').Date
master_nifty_data = master_nifty_data[master_nifty_data['Date'].isin(valid_trading_dates)]
# master_nifty_data.tail(10)
gmdp = pd.read_excel(r'\\*************\c$\Users\Administrator\PycharmProjects\Projects\globalMacroDataPipeline\Historic Macro Data2.xlsx')
Nifty_PE_PB = d.fetch_data_from_database(table_name='Nifty_PE_PB', start_date='2006-01-01')
finance_fr = d.fetch_data_from_database(table_name='finance_fr')
Companymaster = d.fetch_data_from_database(table_name='Companymaster')



from indicators import LensMarketBreadthIndicators

mbi = LensMarketBreadthIndicators(price_data, master_nifty_data, smooth=10) 
dist_52w_high = mbi.distance_52w_high()
dist_alltime_high = mbi.distance_all_time_high()
beating_nifty_signal = mbi.beating_benchmark_signal()
nifty_eqwt_spreads = mbi.nifty_eqwt_spreads()
new_highs_12m = mbi.new_highs_12m()
highs_lows_52w = mbi.highs_lows_52w().filter(like='High')
breadth_alpha, breadth_absolute = mbi.breadth_alpha_absolute()

# concatenate all the above dataframes
all_mb = pd.concat([dist_52w_high, dist_alltime_high, beating_nifty_signal, nifty_eqwt_spreads, \
                    new_highs_12m, highs_lows_52w, breadth_alpha, breadth_absolute], axis=1, join='inner')

sentimeter = (all_mb.rolling(252).rank(pct=True)).mean(axis=1).rank(pct=True)

plt.figure(figsize=(12, 4))
plt.title('Sentimeter')
plt.plot(sentimeter, color='navy')
plt.axhline(y=0.5, color='r', linestyle='--')
plt.grid(True)
plt.tight_layout()
plt.show()

from indicators import LensValuationIndicators

vali = LensValuationIndicators(price_data=price_data, fundamentals=Value_rawdata,finance_fr=finance_fr, company_master=Companymaster, smooth=10)
above_historical_valuation = vali.compute_stocks_above_historical_valuations()
valuation_premium_discount = vali.valuation_premium_discount()
earnings_vs_bond_yield_inverse_spread = vali.earnings_vs_bond_yield_inverse_spread(Nifty_PE_PB, gmdp[['Date', 'India 10Y']])
dividend_yield_inverse = vali.build_dividend_yield_inverse_series()

all_val = pd.concat([above_historical_valuation, valuation_premium_discount,\
                    earnings_vs_bond_yield_inverse_spread, dividend_yield_inverse], axis=1, join='inner')

valuemeter = (all_val.rolling(252).rank(pct=True, ascending=True)).mean(axis=1).rank(pct=True)  ## anti (original : ascending=False)
plt.figure(figsize=(12, 4))
plt.title('Valuemeter')
plt.plot(valuemeter, color='navy')
plt.axhline(y=0.5, color='r', linestyle='--')
plt.grid(True)
plt.tight_layout()
plt.show()


